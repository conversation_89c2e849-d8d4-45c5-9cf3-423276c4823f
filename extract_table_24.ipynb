# Install required packages if not already installed
!pip install pandas numpy pillow pytesseract opencv-python matplotlib

# Import required libraries
import pandas as pd
import numpy as np
from PIL import Image
import pytesseract
import cv2
import matplotlib.pyplot as plt
import re
from io import StringIO



# Load and display the image
image_path = 'sample.jpg'
image = Image.open(image_path)

# Display the image
plt.figure(figsize=(12, 8))
plt.imshow(image)
plt.axis('off')
plt.title('Original Image with Table No. 24')
plt.show()

print(f"Image size: {image.size}")

# Preprocess the image for better OCR results
def preprocess_image(image_path):
    # Read image with OpenCV
    img = cv2.imread(image_path)
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Apply threshold to get better contrast
    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # Remove noise
    denoised = cv2.medianBlur(thresh, 3)
    
    return denoised

# Preprocess the image
processed_img = preprocess_image(image_path)

# Display preprocessed image
plt.figure(figsize=(12, 8))
plt.imshow(processed_img, cmap='gray')
plt.axis('off')
plt.title('Preprocessed Image')
plt.show()

# Extract text using OCR
def extract_text_from_image(image):
    # Configure tesseract for better table recognition
    custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,()%- '
    
    # Extract text
    text = pytesseract.image_to_string(image, config=custom_config)
    
    return text

# Extract text from both original and processed images
original_text = extract_text_from_image(image)
processed_text = extract_text_from_image(processed_img)

print("Text extracted from original image:")
print("=" * 50)
print(original_text)
print("\n" + "=" * 50)
print("\nText extracted from processed image:")
print("=" * 50)
print(processed_text)

# Function to parse table data from extracted text
def parse_table_data(text):
    lines = text.strip().split('\n')
    
    # Find table 24 section
    table_start = -1
    for i, line in enumerate(lines):
        if 'table' in line.lower() and '24' in line:
            table_start = i
            break
    
    if table_start == -1:
        print("Table 24 not found in the text. Attempting to parse all tabular data...")
        table_start = 0
    
    # Extract table data (this is a basic parser - may need adjustment based on actual table format)
    table_data = []
    headers = []
    
    for i in range(table_start, len(lines)):
        line = lines[i].strip()
        if not line:
            continue
            
        # Split by multiple spaces or tabs to separate columns
        columns = re.split(r'\s{2,}|\t', line)
        columns = [col.strip() for col in columns if col.strip()]
        
        if len(columns) > 1:
            if not headers:
                headers = columns
            else:
                table_data.append(columns)
    
    return headers, table_data

# Parse table data from the better text extraction
headers, table_data = parse_table_data(processed_text if len(processed_text) > len(original_text) else original_text)

print("Detected headers:", headers)
print("\nFirst few rows of data:")
for i, row in enumerate(table_data[:5]):
    print(f"Row {i+1}: {row}")

# Create DataFrame from extracted data
def create_dataframe(headers, table_data):
    if not headers or not table_data:
        print("No table data found. Creating empty DataFrame.")
        return pd.DataFrame()
    
    # Ensure all rows have the same number of columns as headers
    max_cols = len(headers)
    cleaned_data = []
    
    for row in table_data:
        # Pad or truncate row to match header length
        if len(row) < max_cols:
            row.extend([''] * (max_cols - len(row)))
        elif len(row) > max_cols:
            row = row[:max_cols]
        cleaned_data.append(row)
    
    # Create DataFrame
    df = pd.DataFrame(cleaned_data, columns=headers)
    
    return df

# Create the DataFrame
df_table24 = create_dataframe(headers, table_data)

print(f"DataFrame shape: {df_table24.shape}")
print("\nDataFrame info:")
print(df_table24.info())
print("\nFirst 10 rows of Table 24:")
display(df_table24.head(10))

# Data cleaning and type conversion
def clean_dataframe(df):
    if df.empty:
        return df
    
    # Remove completely empty rows
    df = df.dropna(how='all')
    
    # Clean numeric columns (remove commas, convert percentages, etc.)
    for col in df.columns:
        # Try to convert to numeric if possible
        df[col] = df[col].astype(str).str.replace(',', '')
        
        # Handle percentage values
        if df[col].str.contains('%').any():
            df[col] = df[col].str.replace('%', '')
            df[col] = pd.to_numeric(df[col], errors='ignore')
        else:
            df[col] = pd.to_numeric(df[col], errors='ignore')
    
    return df

# Clean the DataFrame
df_table24_cleaned = clean_dataframe(df_table24.copy())

print("Cleaned DataFrame:")
print(f"Shape: {df_table24_cleaned.shape}")
display(df_table24_cleaned)

# Display basic statistics if there are numeric columns
numeric_cols = df_table24_cleaned.select_dtypes(include=[np.number]).columns
if len(numeric_cols) > 0:
    print("\nBasic statistics for numeric columns:")
    display(df_table24_cleaned[numeric_cols].describe())

# Save the DataFrame to CSV for future use
output_file = 'table_24_data.csv'
df_table24_cleaned.to_csv(output_file, index=False)
print(f"Table 24 data saved to: {output_file}")

# Display final summary
print("\n" + "="*60)
print("SUMMARY - TABLE 24 DATA EXTRACTION")
print("="*60)
print(f"Total rows extracted: {len(df_table24_cleaned)}")
print(f"Total columns: {len(df_table24_cleaned.columns)}")
print(f"Column names: {list(df_table24_cleaned.columns)}")
print(f"Data saved to: {output_file}")
print("="*60)